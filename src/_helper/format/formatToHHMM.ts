export function formatDateToHHMM(dateString: string): string {
  const date = new Date(dateString);
  const hours = date.getUTCHours();
  const minutes = date.getUTCMinutes();

  // Pad single digit minutes with a leading zero
  const formattedHours = hours.toString().padStart(2, "0");
  const formattedMinutes = minutes.toString().padStart(2, "0");

  return `${formattedHours}:${formattedMinutes}`;
}

/**
 * Formats a price value with TND currency
 * @param price - The price value to format
 * @returns Formatted price string with TND currency
 */
export function formatPrice(price?: number): string {
  if (!price && price !== 0) return "0.00 TND";
  return `${price.toFixed(2)} TND`;
}
