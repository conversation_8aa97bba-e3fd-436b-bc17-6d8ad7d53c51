import { scale } from "@/src/_helper/Scaler";
import Loader from "@/src/components/loader/Loader";
import { DeleteConfirmationModal } from "@/src/components/model/DeleteConfirmationModal";
import { ErrorComponent } from "@/src/components/ui/ErrorComponent";
import { ThemedText } from "@/src/components/ui/ThemedText";
import { Colors } from "@/src/constants/Colors";
import { useLanguage } from "@/src/context/LanguageContext";
import { useTheme } from "@/src/context/ThemeContext";
import useDeleteProductMutation from "@/src/services/querys/vendor/useDeleteProductMutation";
import useGetProductDetails from "@/src/services/querys/vendor/useGetProductDetails";
import { MaterialIcons } from "@expo/vector-icons";
import { useLocalSearchParams, useRouter } from "expo-router";
import React, { useState } from "react";
import {
  Dimensions,
  FlatList,
  Image,
  ImageSourcePropType,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";

interface ProductImage {
  id: string;
  source: ImageSourcePropType;
}

const ProductDetails: React.FC = () => {
  const { currentTheme } = useTheme();
  const { t } = useLanguage();
  const theme = currentTheme ?? "dark";
  const styles = getStyles(theme);
  const router = useRouter();
  const { id } = useLocalSearchParams() as { id: string };
  const { data, isLoading, isError, error, refetch } = useGetProductDetails(id);
  const deleteProductMutation = useDeleteProductMutation();
  const handleRefetch = async () => await refetch(); // Refetch the product details
  // State for delete modal
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const handleEditPress = (): void => {
    router.push(`/product/${id}/edit-product`);
  };

  const handleDeletePress = (): void => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async (): Promise<void> => {
    try {
      await deleteProductMutation.mutateAsync({ id: data?.data?.product?._id });

      // Close modal and navigate back on success
      setShowDeleteModal(false);
    } catch (error) {
      console.error("Delete error:", error);
    }
  };

  const handleDeleteCancel = (): void => {
    setShowDeleteModal(false);
  };

  // Transform images array to the expected format
  const productImages: ProductImage[] = React.useMemo(() => {
    if (!data?.data?.product?.images) return [];

    return data?.data?.product?.images.map(
      (imageUrl: string, index: number) => ({
        id: `${data?.data?.product?._id}-${index}`,
        source: { uri: imageUrl },
      })
    );
  }, [data]);

  const renderImageItem = ({ item }: { item: ProductImage }) => (
    <View
      style={[
        styles.imageContainer,
        { backgroundColor: Colors[theme].primary },
      ]}
    >
      <Image source={item.source} style={styles.productImage} />
    </View>
  );

  // Loading state
  if (isLoading) {
    return <Loader />;
  }

  // Error state
  if (isError) {
    return (
      <ErrorComponent
        error={t("backend.server_error")}
        onRetry={handleRefetch}
      />
    );
  }

  // No data state
  if (!data?.data?.product) {
    return (
      <ErrorComponent
        error={t("vendor.productDetails.errors.noProductFound")}
        onRetry={handleRefetch}
      />
    );
  }

  const product = data?.data?.product;

  return (
    <View style={styles.container}>
      {/* Floating Action Buttons */}
      <TouchableOpacity
        onPress={handleEditPress}
        style={styles.floatingEditButton}
        activeOpacity={0.7}
      >
        <MaterialIcons name="edit" size={24} color={Colors[theme].primary} />
      </TouchableOpacity>

      <TouchableOpacity
        onPress={handleDeletePress}
        style={styles.floatingDeleteButton}
        activeOpacity={0.7}
        disabled={deleteProductMutation.isPending}
      >
        <MaterialIcons name="delete" size={24} color={Colors[theme].error} />
      </TouchableOpacity>

      <ScrollView
        style={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContentContainer}
      >
        {/* Images Section */}
        {productImages.length > 0 && (
          <View style={styles.imagesSection}>
            <FlatList
              data={productImages}
              renderItem={renderImageItem}
              keyExtractor={(item) => item.id}
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.imagesList}
              ItemSeparatorComponent={() => (
                <View style={styles.imageSeparator} />
              )}
            />
          </View>
        )}

        <View style={styles.productInfo}>
          {/* Product Name and Description */}
          <View style={styles.section}>
            <ThemedText type="bold" size={20} style={styles.productName}>
              {product?.name ||
                t("vendor.productDetails.labels.unnamedProduct")}
            </ThemedText>
            {product?.description && (
              <ThemedText
                size={14}
                style={[
                  styles.productDescription,
                  { color: Colors[theme].secondary },
                ]}
              >
                {product?.description}
              </ThemedText>
            )}
          </View>

          {/* Brand */}
          {product?.brand && (
            <View style={styles.section}>
              <ThemedText
                type="semi-bold"
                size={16}
                style={styles.sectionTitle}
              >
                {t("vendor.productDetails.labels.brand")}
              </ThemedText>
              <ThemedText
                size={14}
                style={[
                  styles.sectionValue,
                  { color: Colors[theme].secondary },
                ]}
              >
                {product?.brand?.name}
              </ThemedText>
              {product?.brand?.description && (
                <ThemedText
                  size={12}
                  style={[
                    styles.brandDescription,
                    { color: Colors[theme].secondary, marginTop: 4 },
                  ]}
                >
                  {product?.brand?.description}
                </ThemedText>
              )}
            </View>
          )}

          {/* Details */}
          <View style={styles.section}>
            <ThemedText type="semi-bold" size={16} style={styles.sectionTitle}>
              {t("vendor.productDetails.labels.details")}
            </ThemedText>

            <View style={styles.detailsGrid}>
              <View style={styles.detailRow}>
                <View style={styles.detailItem}>
                  <ThemedText
                    size={14}
                    style={[
                      styles.detailLabel,
                      { color: Colors[theme].secondary },
                    ]}
                  >
                    {t("vendor.productDetails.labels.category")}
                  </ThemedText>
                  <ThemedText size={14}>
                    {product?.category?.name || t("vendor.labels.notSpecified")}
                  </ThemedText>
                </View>
                <View style={styles.detailItem}>
                  <ThemedText
                    size={14}
                    style={[
                      styles.detailLabel,
                      { color: Colors[theme].secondary },
                    ]}
                  >
                    {t("vendor.productDetails.labels.price")}
                  </ThemedText>
                  <ThemedText size={14}>
                    {product?.price?.toFixed(2) || "0.00"} TND
                  </ThemedText>
                </View>
              </View>

              <View style={styles.detailRow}>
                <View style={styles.detailItem}>
                  <ThemedText
                    size={14}
                    style={[
                      styles.detailLabel,
                      { color: Colors[theme].secondary },
                    ]}
                  >
                    Images
                  </ThemedText>
                  <ThemedText size={14}>
                    {product?.images?.length || 0}
                  </ThemedText>
                </View>
              </View>

              <View style={styles.detailRow}>
                <View style={styles.detailItem}>
                  <ThemedText
                    size={14}
                    style={[
                      styles.detailLabel,
                      { color: Colors[theme].secondary },
                    ]}
                  >
                    {t("vendor.productDetails.labels.created")}
                  </ThemedText>
                  <ThemedText size={14}>
                    {product?.createdAt
                      ? new Date(product?.createdAt).toLocaleDateString()
                      : t("vendor.productDetails.labels.unknown")}
                  </ThemedText>
                </View>
                <View style={styles.detailItem}>
                  <ThemedText
                    size={14}
                    style={[
                      styles.detailLabel,
                      { color: Colors[theme].secondary },
                    ]}
                  >
                    {t("vendor.productDetails.labels.updated")}
                  </ThemedText>
                  <ThemedText size={14}>
                    {product?.updatedAt
                      ? new Date(product?.updatedAt).toLocaleDateString()
                      : t("vendor.productDetails.labels.unknown")}
                  </ThemedText>
                </View>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        visible={showDeleteModal}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        isLoading={deleteProductMutation.isPending}
        itemName={product?.name || "this product"}
        itemType="product"
        title={t("vendor.productDetails.modal.title")}
        message={t("vendor.productDetails.modal.message")}
        confirmText={t("vendor.productDetails.modal.confirmText")}
        cancelText={t("vendor.productDetails.modal.cancelText")}
      />
    </View>
  );
};

const getStyles = (theme: "light" | "dark") =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    centered: {
      justifyContent: "center",
      alignItems: "center",
    },
    scrollContent: {
      flex: 1,
    },
    scrollContentContainer: {
      paddingBottom: scale(20), // Reduced since no bottom buttons
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      paddingBottom: scale(20),
    },
    backButton: {
      width: scale(40),
      height: scale(40),
      borderRadius: scale(20),
      alignItems: "center",
      justifyContent: "center",
    },
    imagesSection: {
      marginBottom: scale(24),
    },
    imagesList: {
      paddingHorizontal: 0,
    },
    imageSeparator: {
      width: scale(12),
    },
    imageContainer: {
      width: Dimensions.get("window").width * 0.45,
      height: scale(200),
      borderRadius: scale(16),
      alignItems: "center",
      justifyContent: "center",
      overflow: "hidden",
    },
    productImage: {
      width: "100%",
      height: "100%",
      resizeMode: "cover",
    },
    productInfo: {
      flex: 1,
    },
    section: {
      marginBottom: scale(20),
    },
    productName: {
      marginBottom: scale(4),
    },
    productDescription: {
      lineHeight: scale(18),
    },
    brandDescription: {
      lineHeight: scale(16),
      fontStyle: "italic",
    },
    sectionTitle: {
      marginBottom: scale(8),
    },
    sectionValue: {
      lineHeight: scale(18),
    },
    detailsGrid: {
      gap: scale(12),
    },
    detailRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      gap: scale(16),
    },
    detailItem: {
      flex: 1,
    },
    detailLabel: {
      marginBottom: scale(4),
    },
    // Floating Action Buttons
    floatingEditButton: {
      position: "absolute",
      top: scale(50),
      right: scale(20),
      zIndex: 1000,
      padding: scale(10),
      backgroundColor: Colors[theme].background,
      borderRadius: scale(25),
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    floatingDeleteButton: {
      position: "absolute",
      top: scale(50),
      left: scale(20),
      zIndex: 1000,
      padding: scale(10),
      backgroundColor: Colors[theme].background,
      borderRadius: scale(25),
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
  });

export default ProductDetails;
