import { scale } from "@/src/_helper/Scaler";
import { Colors } from "@/src/constants/Colors";
import { useTheme } from "@/src/context/ThemeContext";
import { router } from "expo-router";
import React from "react";
import { Image, StyleSheet, TouchableOpacity, View } from "react-native";
import { ThemedText } from "./ThemedText";

interface ProductReviewCardProps {
  product: {
    _id: string;
    name: string;
    images: string[];
    price: number;
  };
}

const ProductReviewCard: React.FC<ProductReviewCardProps> = ({ product }) => {
  console.log("sss", product);
  const { currentTheme } = useTheme();
  const theme = currentTheme ?? "dark";
  const styles = getStyles(theme);

  const handlePress = () => {
    router.push(`/(client)/(screens)/products/${product?._id}/product`);
  };

  return (
    <TouchableOpacity style={styles.container} onPress={handlePress}>
      <View style={styles.imageContainer}>
        <Image
          source={
            product?.images && product?.images.length > 0
              ? { uri: product?.images[0] }
              : require("@/src/assets/images/icon.png")
          }
          style={styles.productImage}
          resizeMode="cover"
        />
      </View>
      <View style={styles.productInfo}>
        <ThemedText type="semi-bold" size={14} numberOfLines={2}>
          {product?.name}
        </ThemedText>
        <ThemedText
          type="bold"
          size={16}
          style={[styles.price, { color: Colors[theme].primary }]}
        >
          {product?.price ? product.price.toFixed(2) : "0.00"} TND
        </ThemedText>
      </View>
    </TouchableOpacity>
  );
};

const getStyles = (theme: "light" | "dark") =>
  StyleSheet.create({
    container: {
      flexDirection: "row",
      backgroundColor: Colors[theme].background,
      borderRadius: scale(12),
      padding: scale(12),
      marginTop: scale(8),
      borderWidth: 1,
      borderColor: Colors[theme].border,
    },
    imageContainer: {
      width: scale(60),
      height: scale(60),
      borderRadius: scale(8),
      backgroundColor: Colors[theme].secondary,
      justifyContent: "center",
      alignItems: "center",
      overflow: "hidden",
    },
    productImage: {
      width: "100%",
      height: "100%",
    },
    productInfo: {
      flex: 1,
      marginLeft: scale(12),
      justifyContent: "space-between",
    },
    price: {
      marginTop: scale(4),
    },
  });

export default ProductReviewCard;
